document.addEventListener('DOMContentLoaded', async function () {
    const urlParams = new URLSearchParams(window.location.search);
    const id = urlParams.get('id');

    if (!window.API_URL_PHP) {
        console.error('API_URL_PHP no está definida');
        return;
    }

    if (id) {
        // Mostrar perfil individual
        try {
            const response = await fetch(`${window.API_URL_PHP}controller/get_perfil_creativo.php?id=${id}`);
            const result = await response.json();

            if (!result.exito) {
                console.error('Error al obtener el perfil:', result.mensaje);
                document.getElementById('lista-creativos').innerHTML = `
                    <div class="sin-resultados">
                        <p>No se encontró el perfil solicitado.</p>
                    </div>
                `;
                return;
            }

            const creativo = result.creativo;
            let imgSrc = '../assets/default-profile.jpg';
            if (creativo.foto_perfil) imgSrc = creativo.foto_perfil;

            const descripcionCorta = creativo.descripcion && creativo.descripcion.length > 120
                ? creativo.descripcion.substring(0, 120) + '...'
                : (creativo.descripcion || '');

            const card = document.createElement('div');
            card.className = 'creative-card';
            card.innerHTML = `
                <div class="creative-image">
                    <img src="${imgSrc}" alt="${creativo.nombre} ${creativo.apellido}" loading="lazy">
                </div>
                <h3 class="creative-name">${creativo.nombre} ${creativo.apellido}</h3>
                <p class="creative-role">${creativo.titulo_profesional}</p>
                <p class="creative-quote">"${descripcionCorta}"</p>
            `;
            const listaContainer = document.getElementById('lista-creativos');
            listaContainer.innerHTML = '';
            listaContainer.appendChild(card);
        } catch (error) {
            console.error('Error al cargar el perfil:', error);
            document.getElementById('lista-creativos').innerHTML = `
                <div class="sin-resultados">
                    <p>Error al cargar el perfil.</p>
                </div>
            `;
        }
    } else {
        // Mostrar lista de creativos
        cargarCreativos();
    }
});

async function cargarCreativos() {
    try {
        const response = await fetch(`${window.API_URL_PHP}controller/get_creativos.php`);
        const data = await response.json(); 

        const listaContainer = document.getElementById('lista-creativos');

        const cargando = document.querySelector('.cargando');
        if (cargando) cargando.remove();

        if (data.exito && data.creativos.length > 0) {
            data.creativos.forEach((creativo, index) => {
                let imgSrc = '../assets/default-profile.jpg';
                if (creativo.foto_perfil) imgSrc = creativo.foto_perfil;

                const descripcionCorta = creativo.descripcion && creativo.descripcion.length > 120
                    ? creativo.descripcion.substring(0, 120) + '...'
                    : (creativo.descripcion || '');

                const card = document.createElement('div');
                card.className = 'creative-card';

                card.innerHTML = `
                    <div class="creative-image">
                        <img src="${imgSrc}" alt="${creativo.nombre} ${creativo.apellido}" loading="lazy">
                    </div>
                    <h3 class="creative-name">${creativo.nombre} ${creativo.apellido}</h3>
                    <p class="creative-role">${creativo.titulo_profesional}</p>
                    <p class="creative-quote">"${descripcionCorta}"</p>
                    <button class="btn-perfil" onclick="verPerfil('${creativo.id_pcreativa}')">Ver Perfil</button>
                `;
                listaContainer.appendChild(card);
            });
        } else {
            listaContainer.innerHTML = `<div class="sin-resultados"><p>No se encontraron perfiles.</p></div>`;
        }
    } catch (error) {
        console.error('Error al cargar creativos:', error);
        document.getElementById('lista-creativos').innerHTML = `
            <div class="sin-resultados">
                <p>Error al cargar creativos.</p>
            </div>
        `;
    }
}

function verPerfil(id) {
    window.location.href = `./perfil_creativo.html?id=${id}`;
}
