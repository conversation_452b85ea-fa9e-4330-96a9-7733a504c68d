// Funciones globales para acceder desde HTML
function verDetallesContacto(idContacto) {
    fetch(`${window.API_URL_PHP}controller/get_respuestas.php?id_contacto=${idContacto}`)
        .then(res => res.json())
        .then(respuestas => {
            mostrarModalDetalles(idContacto, respuestas);
        })
        .catch(error => {
            console.error('Error al obtener respuestas:', error);
            alert('Error al cargar los detalles del contacto');
        });
}

function mostrarModalDetalles(idContacto, respuestas) {
    const modal = document.getElementById('detalles-modal');
    const modalContent = document.getElementById('detalles-modal-content');
    
    let html = `
        <div class="modal-header">
            <h2>Detalles del match </h2>
            <span class="close" onclick="cerrarModalDetalles()">&times;</span>
        </div>
    `;

    if (respuestas && respuestas.length > 0) {
        respuestas.forEach((respuesta, index) => {
            html += `
                <div class="respuesta-item">
                    <h3>Respuesta del creativo</h3>
                    <div class="respuesta-details">
                        <p><strong>Fecha Opción 1:</strong> ${respuesta.fecha_opcion_1 || 'No especificada'}</p>
                        <p><strong>Fecha Opción 2:</strong> ${respuesta.fecha_opcion_2 || 'No especificada'}</p>
                        <p><strong>Fecha Opción 3:</strong> ${respuesta.fecha_opcion_3 || 'No especificada'}</p>
                        <p><strong>Modo de Contacto:</strong> ${respuesta.modo_contacto}</p>
                        ${respuesta.modo_contacto === 'presencial' ? `<p><strong>Dirección física:</strong> ${respuesta.detalle_contacto || 'No especificada'}</p>` : ''}
                        ${respuesta.modo_contacto === 'videollamada' ? `<p><strong>Link Videollamada:</strong> <a href="${respuesta.link_videollamada}" target="_blank">${respuesta.link_videollamada}</a></p>` : ''}
                        ${respuesta.modo_contacto === 'agente' ? `<p><strong>Tipo de Apoyo:</strong> ${respuesta.tipo_apoyo || 'No especificado'}</p>` : ''}
                        <p><strong>Creado:</strong> ${respuesta.creado_en}</p>
                    </div>
                </div>
            `;
        });
    } else {
        html += '<p class="no-respuestas">No hay respuestas registradas para este contacto.</p>';
    }

    modalContent.innerHTML = html;
    modal.style.display = 'flex';
}

function cerrarModalDetalles() {
    const modal = document.getElementById('detalles-modal');
    modal.style.display = 'none';
}

// Cerrar modal al hacer clic fuera de él
window.onclick = function(event) {
    const modal = document.getElementById('detalles-modal');
    if (event.target === modal) {
        modal.style.display = 'none';
    }
}

document.addEventListener('DOMContentLoaded', function() {
    const url = '../../Backend/controller/get_contactos.php';
    const tarjetasContainer = document.getElementById('tarjetas-container');
    const tablaContainer = document.getElementById('tabla-container');
    const adminTablaContainer = document.getElementById('admin-tabla-container');
    const estadoPerfilesContainer = document.getElementById('estado-perfiles-container');
    const tabTarjetas = document.getElementById('tab-tarjetas');
    const tabTabla = document.getElementById('tab-tabla');
    const tabAdminTabla = document.getElementById('tab-admin-tabla');
    const tabEstadoPerfiles = document.getElementById('tab-estado-perfiles');

    function mostrarTarjetas(contactos, estadoFiltro = 'todos') {
        tarjetasContainer.innerHTML = '';

        const filtrados = estadoFiltro === 'todos' 
            ? contactos 
            : contactos.filter(c => (c.estado || '') === estadoFiltro);

        filtrados.forEach(c => {
            const card = document.createElement('div');
            card.className = 'contacto-card';

            const estado = c.estado || 'Sin estado';
            const estadoClass = estado.toLowerCase().replace(/\s+/g, '-');

            card.innerHTML = `
                <div class="estado-badge ${estadoClass}">${estado}</div>

                <div class="card-line"><span class="label">📛 Nombre:</span><span>${c.nombre}</span></div>
                <div class="card-line"><span class="label">📞 Teléfono:</span><span>${c.telefono || '-'}</span></div>
                <div class="card-line"><span class="label">✉️ Email:</span><span>${c.email || '-'}</span></div>
                <div class="card-line"><span class="label">📝 Descripción:</span><span>${c.descripcion || '-'}</span></div>
                <div class="card-line"><span class="label">📅 Desde:</span><span>${c.fechainicio || '-'}</span></div>
                <div class="card-line"><span class="label">📅 Hasta:</span><span>${c.fechatermino || '-'}</span></div>
                <div class="card-line"><span class="label">🎨 Creativo:</span><span>${(c.nombre_creativo || '-') + (c.apellido_creativo ? ' ' + c.apellido_creativo : '')}</span></div>
                
                <div class="card-actions">
                    <button class="ver-mas-btn" onclick="verDetallesContacto(${c.id})">Ver más</button>
                </div>
            `;

            tarjetasContainer.appendChild(card);
        });
    }

    function mostrarTabla(contactos, admin = false) {
        let html = '<table class="contactos-table"><thead><tr>';
        html += '<th>ID</th><th>Nombre</th><th>Teléfono</th><th>Email</th><th>Descripción</th><th>Fecha inicio</th><th>Fecha término</th><th>Creativo</th><th>Estado</th>';
        html += '</tr></thead><tbody>';
        contactos.forEach(c => {
            html += `<tr>
                <td>${c.id}</td>
                <td>${c.nombre}</td>
                <td>${c.telefono || '-'}</td>
                <td>${c.email || '-'}</td>
                <td>${c.descripcion || '-'}</td>
                <td>${c.fechainicio || '-'}</td>
                <td>${c.fechatermino || '-'}</td>
                <td>${(c.nombre_creativo || '-') + (c.apellido_creativo ? ' ' + c.apellido_creativo : '')}</td>
                <td>
                    <select class="estado-select" data-id="${c.id}">
                        <option class="sin_estado" value="Sin Estado" ${c.estado === 'Sin Estado' ? 'selected' : ''}>Sin Estado</option>
                        <option class="inicial" value="Contacto Inicial" ${c.estado === 'Contacto Inicial' ? 'selected' : ''}>Contacto Inicial</option>
                        <option class="minicial" value="Match Inicial" ${c.estado === 'Match Inicial' ? 'selected' : ''}>Match Inicial</option>
                        <option class="confirmado" value="Match Confirmado" ${c.estado === 'Match Confirmado' ? 'selected' : ''}>Match Confirmado</option>
                        <option class="rechazado" value="Match Rechazado" ${c.estado === 'Match Rechazado' ? 'selected' : ''}>Match Rechazado</option>
                        <option class="inactivo" value="Match Inactivo" ${c.estado === 'Match Inactivo' ? 'selected' : ''}>Match Inactivo</option> 
                        <option class="finalizado" value="Match Finalizado" ${c.estado === 'Match Finalizado' ? 'selected' : ''}>Match Finalizado</option> 
                    </select>
                </td>
            </tr>`;
        });
        html += '</tbody></table>';
        if(admin) adminTablaContainer.innerHTML = html;
        else tablaContainer.innerHTML = html;

        // Evento para guardar el estado al cambiar
        document.querySelectorAll('.estado-select').forEach(select => {
            select.addEventListener('change', function() {
                const id = this.getAttribute('data-id');
                const estado = this.value;
                const prevValue = this.getAttribute('data-prev') || this.defaultValue;

                // Buscar el modal correctamente
                const modal = document.getElementById('confirm-modal');
                if (!modal) {
                    alert('No se encontró el modal de confirmación en el HTML.');
                    return;
                }

                modal.style.display = 'flex';

                // Botones
                const btnConfirm = document.getElementById('confirm-btn');
                const btnCancel = document.getElementById('cancel-btn');

                btnConfirm.onclick = null;
                btnCancel.onclick = null;

                btnConfirm.onclick = () => {
                    fetch(`${window.API_URL_PHP}controller/update_estado_contacto.php`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ id, estado })
                    })
                    .then(res => res.json())
                    .then(resp => {
                        if (!resp.exito) {
                            alert('Error al guardar el estado: ' + (resp.error || ''));
                            select.value = prevValue;
                        } else {
                            select.setAttribute('data-prev', estado);
                        }
                        modal.style.display = 'none';
                    })
                    .catch(() => {
                        alert('Error de red al guardar el estado');
                        select.value = prevValue;
                        modal.style.display = 'none';
                    });
                };
                btnCancel.onclick = () => {
                    select.value = prevValue;
                    modal.style.display = 'none';
                };
            });
            select.setAttribute('data-prev', select.value);
        });

        document.querySelectorAll('.estado-select').forEach(select => {
            function setBadgeClass() {
                select.classList.remove('inicial', 'minicial', 'confirmado', 'rechazado', 'inactivo', 'finalizado');
                switch (select.value) {
                    case 'Sin Estado':
                        select.classList.add('Sin_Estado');
                        break;
                    case 'Contacto Inicial':
                        select.classList.add('inicial');
                        break;
                    case 'Match Inicial':
                        select.classList.add('minicial');
                        break;
                    case 'Match Confirmado':
                        select.classList.add('confirmado');
                        break;
                    case 'Match Rechazado':
                        select.classList.add('rechazado');
                        break;
                    case 'Match Inactivo':
                        select.classList.add('inactivo');
                        break;
                    case 'Match Finalizado':
                        select.classList.add('finalizado');
                        break;
                }
            }
            select.addEventListener('change', setBadgeClass);
            setBadgeClass(); // Inicializa al cargar
        });
    }

    let contactosGlobal = [];

    async function cargarPerfiles() {
        estadoPerfilesContainer.innerHTML = '<p>Cargando perfiles...</p>';

        try {
            const response = await fetch('../../Backend/controller/get_perfiles.php');
            const perfiles = await response.json();

            let html = `<table class="contactos-table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Nombre</th>
                                    <th>Título</th>
                                    <th>Teléfono</th>
                                    <th>Correo</th>
                                    <th>Estado</th>
                                </tr>
                            </thead>
                        <tbody>`;

            perfiles.forEach((p) => {
                html += `
                            <tr>
                                <td>${p.id_pcreativa}</td>
                                <td>${p.nombre} ${p.apellido}</td>
                                <td>${p.titulo_profesional || '-'}</td>
                                <td>${p.telefono || '-'}</td>
                                <td>${p.correo || '-'}</td>
                                <td>
                                    <select class="estado-select" data-id="${p.id_pcreativa}">
                                        <option value="1" ${p.estado_perfil == 1 ? 'selected' : ''}>Disponible</option>
                                        <option value="0" ${p.estado_perfil == 0 ? 'selected' : ''}>No disponible</option>
                                    </select>
                                </td>
                            </tr>
                        `;
            });

            html += '</tbody></table>';
            estadoPerfilesContainer.innerHTML = html;

            document.querySelectorAll('.estado-select').forEach((select) => {
                select.addEventListener('change', function () {
                    const id = this.getAttribute('data-id');
                    const nuevoEstado = this.value;
                    const prevValue =
                        this.getAttribute('data-prev') || this.defaultValue;

                    const modal = document.getElementById('confirm-modal');
                    modal.style.display = 'flex';

                    const btnConfirm = document.getElementById('confirm-btn');
                    const btnCancel = document.getElementById('cancel-btn');

                    btnConfirm.onclick = null;
                    btnCancel.onclick = null;

                    btnConfirm.onclick = () => {
                        fetch('../../Backend/controller/update_estado_perfil.php', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({ id, estado_perfil: nuevoEstado }),
                        })
                            .then((res) => res.json())
                            .then((resp) => {
                                if (!resp.exito) {
                                    alert('Error al guardar estado');
                                    select.value = prevValue;
                                } else {
                                    select.setAttribute('data-prev', nuevoEstado);
                                }
                                modal.style.display = 'none';
                            })
                            .catch(() => {
                                alert('Error de red');
                                select.value = prevValue;
                                modal.style.display = 'none';
                            });
                    };

                    btnCancel.onclick = () => {
                        select.value = prevValue;
                        modal.style.display = 'none';
                    };
                });
                select.setAttribute('data-prev', select.value);

                // Colorear el select
                function setBadgeClass() {
                    select.classList.remove('disponible', 'nodisponible');
                    if (select.value == '1') select.classList.add('disponible');
                    else select.classList.add('nodisponible');
                }
                select.addEventListener('change', setBadgeClass);
                setBadgeClass();
            });
        } catch (error) {
            console.error('Error:', error);
            estadoPerfilesContainer.innerHTML =
                '<p>Error al cargar los perfiles.</p>';
        }
    }

    async function cargarContactos() {
        try {
            const response = await fetch('../../Backend/controller/get_contactos.php');
            const contactos = await response.json();
            contactosGlobal = contactos;
            mostrarTarjetas(contactosGlobal);
            mostrarTabla(contactosGlobal);
            mostrarTabla(contactosGlobal, true); // Para admin
        } catch (error) {
            console.error('Error al cargar contactos:', error);
        }
    }

    tabTarjetas.addEventListener('click', function() {
        tarjetasContainer.style.display = 'flex';
        tablaContainer.style.display = 'none';
        adminTablaContainer.style.display = 'none';
    });

    const filtroEstado = document.getElementById('filtro-estado');
    if (filtroEstado) {
        filtroEstado.addEventListener('change', function () {
            mostrarTarjetas(contactosGlobal, this.value);
        });
    }

    const filtroTarjeta = document.getElementById('filter-tarjeta');

    // Botón "Tarjetas"
    tabTarjetas.addEventListener('click', function () {
        tarjetasContainer.style.display = 'flex';
        tablaContainer.style.display = 'none';
        adminTablaContainer.style.display = 'none';
        filtroTarjeta.style.display = 'block'; // mostrar filtro

        // Cambiar clase active
        tabTarjetas.classList.add('active');
        tabAdminTabla.classList.remove('active');
        if (tabTabla) tabTabla.classList.remove('active');
    });

    // Botón "Admin Tabla"
    tabAdminTabla.addEventListener('click', function () {
        tarjetasContainer.style.display = 'none';
        tablaContainer.style.display = 'none';
        adminTablaContainer.style.display = 'block';
        filtroTarjeta.style.display = 'none'; // ocultar filtro

        // Cambiar clase active
        tabTarjetas.classList.remove('active');
        tabAdminTabla.classList.add('active');
        if (tabTabla) tabTabla.classList.remove('active');
    });

    // Si existe "Tabla" también
    if (tabTabla) {
        tabTabla.addEventListener('click', function () {
            tarjetasContainer.style.display = 'none';
            tablaContainer.style.display = 'block';
            adminTablaContainer.style.display = 'none';
            filtroTarjeta.style.display = 'none'; // ocultar filtro

            // Cambiar clase active
            tabTarjetas.classList.remove('active');
            tabAdminTabla.classList.remove('active');
            tabTabla.classList.add('active');
        });
    }

    tabEstadoPerfiles.addEventListener('click', function () {
        tarjetasContainer.style.display = 'none';
        tablaContainer.style.display = 'none';
        adminTablaContainer.style.display = 'none';
        estadoPerfilesContainer.style.display = 'block';

        cargarPerfiles();
    });

    // Inicial
    cargarContactos();
    tabTarjetas.click();
}); 