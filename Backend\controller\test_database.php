<?php
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");
header('Content-Type: application/json');

$resultado = [];

try {
    // Cargar conexión
    require_once __DIR__ . '/../models/conexion.php';
    $resultado['conexion'] = 'exitosa';
    
    // Test 1: Verificar que las tablas existen
    $tablas = ['perfil_creativo', 'contactos', 'categorias'];
    $resultado['tablas'] = [];
    
    foreach ($tablas as $tabla) {
        try {
            $stmt = $conn->prepare("SHOW TABLES LIKE ?");
            $stmt->execute([$tabla]);
            $existe = $stmt->rowCount() > 0;
            $resultado['tablas'][$tabla] = $existe ? 'existe' : 'no existe';
        } catch (Exception $e) {
            $resultado['tablas'][$tabla] = 'error: ' . $e->getMessage();
        }
    }
    
    // Test 2: Contar registros en perfil_creativo
    try {
        $stmt = $conn->prepare("SELECT COUNT(*) as total FROM perfil_creativo");
        $stmt->execute();
        $count = $stmt->fetch(PDO::FETCH_ASSOC);
        $resultado['perfil_creativo_count'] = $count['total'];
    } catch (Exception $e) {
        $resultado['perfil_creativo_count'] = 'error: ' . $e->getMessage();
    }
    
    // Test 3: Contar registros en contactos
    try {
        $stmt = $conn->prepare("SELECT COUNT(*) as total FROM contactos");
        $stmt->execute();
        $count = $stmt->fetch(PDO::FETCH_ASSOC);
        $resultado['contactos_count'] = $count['total'];
    } catch (Exception $e) {
        $resultado['contactos_count'] = 'error: ' . $e->getMessage();
    }
    
    // Test 4: Obtener un registro de ejemplo de perfil_creativo
    try {
        $stmt = $conn->prepare("SELECT id_pcreativa, nombre, apellido, titulo_profesional FROM perfil_creativo LIMIT 1");
        $stmt->execute();
        $ejemplo = $stmt->fetch(PDO::FETCH_ASSOC);
        $resultado['ejemplo_perfil'] = $ejemplo ?: 'sin registros';
    } catch (Exception $e) {
        $resultado['ejemplo_perfil'] = 'error: ' . $e->getMessage();
    }
    
    // Test 5: Información de la base de datos
    try {
        $stmt = $conn->prepare("SELECT DATABASE() as db_name");
        $stmt->execute();
        $db_info = $stmt->fetch(PDO::FETCH_ASSOC);
        $resultado['database_actual'] = $db_info['db_name'];
    } catch (Exception $e) {
        $resultado['database_actual'] = 'error: ' . $e->getMessage();
    }
    
    $resultado['exito'] = true;
    
} catch (Exception $e) {
    $resultado['exito'] = false;
    $resultado['error'] = $e->getMessage();
}

echo json_encode($resultado, JSON_PRETTY_PRINT);
?>
