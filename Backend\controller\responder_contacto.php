<?php
// responder_contacto.php - Controlador que redirige al HTML

// Cargar variables de entorno
require_once __DIR__ . '/../../vendor/autoload.php';
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__ . '/../..');
$dotenv->load();

$token = $_GET['token'] ?? null;

if ($token) {
    // Obtener URL base desde variables de entorno
    $frontend_url = $_ENV['FRONTEND_URL'] ?? 'http://localhost/match-creativo/Frontend';
    // Redirigir al HTML con el token como parámetro
    header("Location: $frontend_url/views/responder_contacto.html?token=" . urlencode($token));
    exit;
} else {
    // Si no hay token, redirigir a la página de inicio
    $base_url = $_ENV['BASE_URL'] ?? 'http://localhost/match-creativo';
    header("Location: $base_url/index.html");
    exit;
}
?>
