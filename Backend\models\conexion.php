<?php


$autoloadPaths = [
    __DIR__ . '/../../vendor/autoload.php',
    __DIR__ . '/../vendor/autoload.php',
    __DIR__ . '/vendor/autoload.php',
    __DIR__ . '/../../../../vendor/autoload.php'
];

$loaded = false;
foreach ($autoloadPaths as $path) {
    if (file_exists($path)) {
        require_once $path;
        $loaded = true;
        break;
    }
}

if (!$loaded) {
    die("No se pudo encontrar el archivo autoload.php. Verifique la instalación de Composer.");
}


$envPaths = [
    __DIR__ . '/../..',
    __DIR__ . '/..',
    __DIR__,
    __DIR__ . '/../../..'
];

$envLoaded = false;
foreach ($envPaths as $path) {
    if (file_exists($path . '/.env')) {
        $dotenv = Dotenv\Dotenv::createImmutable($path);
        $dotenv->load();
        $envLoaded = true;
        break;
    }
}

if (!$envLoaded) {
    die("No se pudo encontrar el archivo .env. Verifique la configuración del proyecto.");
}

$environment = $_ENV['ENVIRONMENT'] ?? 'development';

if($environment === 'production') {  
    $host = $_ENV['PROD_DB_HOST'];
    $port = $_ENV['PROD_DB_PORT'];
    $user = $_ENV['PROD_DB_USER'];
    $password = $_ENV['PROD_DB_PASSWORD'];
    $nameDb = $_ENV['PROD_DB_NAME'];
} else {
    $host = $_ENV['DEV_DB_HOST'];
    $port = $_ENV['DEV_DB_PORT'];
    $user = $_ENV['DEV_DB_USER'];
    $password = $_ENV['DEV_DB_PASSWORD'];
    $nameDb = $_ENV['DEV_DB_NAME'];
}


$dsn = "mysql:host=$host;port=$port;dbname=$nameDb";

$options = [
    PDO::ATTR_ERRMODE            => PDO::ERRMODE_EXCEPTION,
    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    PDO::ATTR_EMULATE_PREPARES   => false,
];

try {
    $conn = new PDO($dsn, $user, $password, $options);
} catch (\PDOException $e) {
   
    throw new \PDOException($e->getMessage(), (int)$e->getCode());
}
