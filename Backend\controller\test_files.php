<?php
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");
header('Content-Type: application/json');

$resultado = [
    'archivos' => [
        '.env' => file_exists(__DIR__ . '/../../.env'),
        'vendor_autoload' => file_exists(__DIR__ . '/../../vendor/autoload.php'),
        'conexion' => file_exists(__DIR__ . '/../models/conexion.php')
    ],
    'rutas' => [
        'directorio_actual' => __DIR__,
        'ruta_env' => __DIR__ . '/../../.env',
        'ruta_vendor' => __DIR__ . '/../../vendor/autoload.php'
    ]
];

echo json_encode($resultado, JSON_PRETTY_PRINT);
?>
