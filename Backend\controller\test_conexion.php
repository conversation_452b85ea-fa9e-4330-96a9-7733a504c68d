<?php
// Archivo de prueba para diagnosticar problemas
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");
header('Content-Type: application/json');

// Habilitar reporte de errores
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo json_encode([
    'paso' => 1,
    'mensaje' => 'PHP funcionando correctamente'
]);

// Verificar si el archivo .env existe
if (!file_exists(__DIR__ . '/../../.env')) {
    echo json_encode([
        'paso' => 2,
        'error' => 'Archivo .env no encontrado',
        'ruta_buscada' => __DIR__ . '/../../.env'
    ]);
    exit;
}

// Verificar si vendor/autoload.php existe
if (!file_exists(__DIR__ . '/../../vendor/autoload.php')) {
    echo json_encode([
        'paso' => 3,
        'error' => 'Archivo vendor/autoload.php no encontrado - ejecutar composer install',
        'ruta_buscada' => __DIR__ . '/../../vendor/autoload.php'
    ]);
    exit;
}

// Intentar cargar dotenv
try {
    require_once __DIR__ . '/../../vendor/autoload.php';
    $dotenv = Dotenv\Dotenv::createImmutable(__DIR__ . '/../..');
    $dotenv->load();
    
    echo json_encode([
        'paso' => 4,
        'mensaje' => 'Dotenv cargado correctamente',
        'environment' => $_ENV['ENVIRONMENT'] ?? 'no definido'
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'paso' => 4,
        'error' => 'Error al cargar dotenv: ' . $e->getMessage()
    ]);
    exit;
}

// Intentar conexión a base de datos
try {
    require_once __DIR__ . '/../models/conexion.php';
    
    echo json_encode([
        'paso' => 5,
        'mensaje' => 'Conexión a base de datos exitosa',
        'conexion_existe' => isset($conn) ? 'SI' : 'NO'
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'paso' => 5,
        'error' => 'Error en conexión: ' . $e->getMessage()
    ]);
    exit;
}
?>
