<?php
// Archivo de prueba para diagnosticar problemas
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");
header('Content-Type: application/json');

// Habilitar reporte de errores
error_reporting(E_ALL);
ini_set('display_errors', 1);

$resultado = [];

// Paso 1: PHP funcionando
$resultado['paso1'] = 'PHP funcionando correctamente';

// Verificar si el archivo .env existe
if (!file_exists(__DIR__ . '/../../.env')) {
    $resultado['error'] = 'Archivo .env no encontrado';
    $resultado['ruta_buscada'] = __DIR__ . '/../../.env';
    echo json_encode($resultado);
    exit;
}
$resultado['paso2'] = 'Archivo .env encontrado';

// Verificar si vendor/autoload.php existe
if (!file_exists(__DIR__ . '/../../vendor/autoload.php')) {
    $resultado['error'] = 'Archivo vendor/autoload.php no encontrado - ejecutar composer install';
    $resultado['ruta_buscada'] = __DIR__ . '/../../vendor/autoload.php';
    echo json_encode($resultado);
    exit;
}
$resultado['paso3'] = 'Vendor encontrado';

// Intentar cargar dotenv
try {
    require_once __DIR__ . '/../../vendor/autoload.php';
    $dotenv = Dotenv\Dotenv::createImmutable(__DIR__ . '/../..');
    $dotenv->load();

    $resultado['paso4'] = 'Dotenv cargado correctamente';
    $resultado['environment'] = $_ENV['ENVIRONMENT'] ?? 'no definido';

} catch (Exception $e) {
    $resultado['error'] = 'Error al cargar dotenv: ' . $e->getMessage();
    echo json_encode($resultado);
    exit;
}

// Intentar conexión a base de datos
try {
    require_once __DIR__ . '/../models/conexion.php';

    $resultado['paso5'] = 'Conexión a base de datos exitosa';
    $resultado['conexion_existe'] = isset($conn) ? 'SI' : 'NO';

} catch (Exception $e) {
    $resultado['error'] = 'Error en conexión: ' . $e->getMessage();
    echo json_encode($resultado);
    exit;
}

// Todo funcionó correctamente
$resultado['exito'] = true;
$resultado['mensaje'] = 'Todas las pruebas pasaron correctamente';

echo json_encode($resultado);
?>
