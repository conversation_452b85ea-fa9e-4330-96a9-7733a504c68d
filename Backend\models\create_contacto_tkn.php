<?php
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: POST, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");
header('Content-Type: application/json');

require_once __DIR__ . '/conexion.php'; // Ajusta ruta si hace falta

use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;

require_once __DIR__ . '/../../vendor/autoload.php';
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__ . '/../../');
$dotenv->load();

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

$data = json_decode(file_get_contents("php://input"), true);

try {
    // Validar campos requeridos
    $required = ['nombre', 'telefono', 'email', 'descripcion', 'fechainicio', 'fechatermino', 'id_perfil_creativo'];
    foreach ($required as $campo) {
        if (empty($data[$campo])) {
            throw new Exception("Falta el campo requerido: $campo");
        }
    }

    // Insertar contacto
    $stmt = $conn->prepare("INSERT INTO contactos (nombre, telefono, email, descripcion, fechainicio, fechatermino, id_perfil_creativo, estado) 
                            VALUES (?, ?, ?, ?, ?, ?, ?, 'Contacto Inicial')");
    $stmt->execute([
        $data['nombre'],
        $data['telefono'],
        $data['email'],
        $data['descripcion'],
        $data['fechainicio'] ?: null,
        $data['fechatermino'] ?: null,
        $data['id_perfil_creativo']
    ]);

    $contacto_id = $conn->lastInsertId();

    // Generar token único
    $token = bin2hex(random_bytes(16)); // 32 chars

    // Guardar token
    $stmt_token = $conn->prepare("INSERT INTO tokens_contacto (id_contacto, token) VALUES (?, ?)");
    $stmt_token->execute([$contacto_id, $token]);

    // Obtener correo del perfil creativo
    $stmt_perfil = $conn->prepare("SELECT correo,telefono FROM perfil_creativo WHERE id_pcreativa = ?");
    $stmt_perfil->execute([$data['id_perfil_creativo']]);
    $perfil = $stmt_perfil->fetch(PDO::FETCH_ASSOC);

    if (!$perfil) {
        throw new Exception("No se encontró el perfil creativo");
    }

    $correo_destino = $perfil['correo'];

    // Formatear fechas a dd/mm/aaaa
    $fecha_inicio_formateada = '';
    $fecha_termino_formateada = '';

    if (!empty($data['fechainicio'])) {
        $dt_inicio = DateTime::createFromFormat('Y-m-d', $data['fechainicio']);
        if ($dt_inicio) {
            $fecha_inicio_formateada = $dt_inicio->format('d/m/Y');
        }
    }

    if (!empty($data['fechatermino'])) {
        $dt_termino = DateTime::createFromFormat('Y-m-d', $data['fechatermino']);
        if ($dt_termino) {
            $fecha_termino_formateada = $dt_termino->format('d/m/Y');
        }
    }

    // Preparar y enviar correo
    $mail = new PHPMailer(true);
    $mail->isSMTP();
    $mail->CharSet = "UTF-8";
    $mail->Host       = $_ENV['MAIL_HOST'];
    $mail->SMTPAuth   = true;
    $mail->Username   = $_ENV['MAIL_USERNAME'];
    $mail->Password   = $_ENV['MAIL_PASSWORD'];
    $mail->SMTPSecure = 'tls';
    $mail->Port       = $_ENV['MAIL_PORT'];

    $mail->setFrom($_ENV['MAIL_FROM'], $_ENV['MAIL_FROM_NAME']);
    $mail->addAddress($correo_destino);

    $link_respuesta = "http://localhost/match-creativo/Backend/controller/responder_contacto.php?token=$token";

    $mail->isHTML(true);
    $mail->Subject = "Nuevo Match de " . htmlspecialchars($data['nombre']);
    $mail->Body = "
        <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; color: #333;'>
            <h2 style='color: #2c3e50;'>🎯 ¡Tienes un nuevo Match Creativo!</h2>
            <p><strong>" . htmlspecialchars($data['nombre']) . "</strong> quiere conectar contigo para un nuevo proyecto.</p>

            <div style='background-color: #f4f8fb; padding: 16px; border-radius: 8px; margin: 20px 0;'>
                <p><strong>El periodo sugerido para contactarnos y trabajar es entre</strong> " . htmlspecialchars($fecha_inicio_formateada) . " <strong> y </strong> " . htmlspecialchars($fecha_termino_formateada) . "    </p>
                
            </div>

            <p>Te invitamos a definir con mayor precisión las fechas que mejor te acomoden para colaborar y avanzar juntos. Para ello, haz clic en el botón a continuación y confirma tu disponibilidad.</p>

            <div style='text-align: center; margin: 30px 0;'>
                <a href='$link_respuesta' style='display: inline-block; padding: 14px 28px; background-color: #007BFF; color: white; text-decoration: none; font-weight: bold; border-radius: 6px;'>Ver Detalles y Confirmar</a>
            </div>

            <p style='font-size: 13px; color: #999;'>Este enlace es único y se desactivará al confirmar tu disponibilidad.</p>
        </div>
    ";

    $mail->send();

    echo json_encode([
    'exito' => true,
    'mensaje' => 'Contacto enviado correctamente y correo notificado',
    'id' => $contacto_id,
    'token' => $token,
    'contacto' => [
        'email' => $correo_destino,
        'telefono' => $perfil['telefono'] ?? null 
    ]
]);


} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'exito' => false,
        'error' => $e->getMessage()
    ]);
}
