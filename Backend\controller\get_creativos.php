<?php
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");
header('Content-Type: application/json');

// Intentar cargar la conexión y capturar cualquier error
try {
    require_once __DIR__ . '/../models/conexion.php';

    // Verificar que la conexión existe
    if (!isset($conn)) {
        echo json_encode([
            'exito' => false,
            'error' => 'Variable de conexión no está definida'
        ]);
        exit;
    }

} catch (Exception $e) {
    echo json_encode([
        'exito' => false,
        'error' => 'Error al cargar conexión: ' . $e->getMessage()
    ]);
    exit;
} catch (Error $e) {
    echo json_encode([
        'exito' => false,
        'error' => 'Error fatal: ' . $e->getMessage()
    ]);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    // Consulta básica para obtener todos los creativos
    $query = "SELECT id_pcreativa, nombre, apellido, titulo_profesional, descripcion, foto_perfil FROM perfil_creativo";
    $params = [];
    
    // Filtrar por categoría si se proporciona
    if (isset($_GET['categoria']) && !empty($_GET['categoria'])) {
        $query .= " WHERE id_categoria = ?";
        $params[] = $_GET['categoria'];
    }
    
    // Ordenar por nombre por defecto
    $query .= " ORDER BY nombre ASC";
    
    $stmt = $conn->prepare($query);
    $stmt->execute($params);
    $creativos = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Procesar las imágenes de perfil
    foreach ($creativos as &$creativo) {
        if ($creativo['foto_perfil']) {
            $creativo['foto_perfil'] = 'data:image/jpeg;base64,' . base64_encode($creativo['foto_perfil']);
        } else {
            $creativo['foto_perfil'] = null;
        }
    }
    
    echo json_encode([
        'exito' => true,
        'creativos' => $creativos
    ]);
    
} catch(PDOException $e) {
    http_response_code(500);
    echo json_encode([
        'exito' => false,
        'error' => 'Error al obtener los creativos: ' . $e->getMessage()
    ]);
}