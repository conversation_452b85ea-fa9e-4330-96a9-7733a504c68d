<?php
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");
header('Content-Type: application/json');

$resultado = [];

// Test 1: PHP básico
$resultado['test1'] = 'PHP funcionando';

// Test 2: Verificar archivos
$archivos = [
    '.env' => file_exists(__DIR__ . '/../../.env'),
    'vendor' => file_exists(__DIR__ . '/../../vendor/autoload.php'),
    'conexion' => file_exists(__DIR__ . '/../models/conexion.php')
];

$resultado['test2'] = 'archivos';
$resultado['archivos'] = $archivos;

// Test 3: Intentar cargar vendor
if ($archivos['vendor']) {
    try {
        require_once __DIR__ . '/../../vendor/autoload.php';
        $resultado['test3'] = 'vendor cargado correctamente';
    } catch (Exception $e) {
        $resultado['test3'] = 'error vendor: ' . $e->getMessage();
    }
} else {
    $resultado['test3'] = 'vendor no existe';
}

// Test 4: Intentar cargar .env
if ($archivos['.env'] && $archivos['vendor']) {
    try {
        $dotenv = Dotenv\Dotenv::createImmutable(__DIR__ . '/../..');
        $dotenv->load();
        $resultado['test4'] = 'dotenv cargado';
        $resultado['env'] = $_ENV['ENVIRONMENT'] ?? 'no definido';
    } catch (Exception $e) {
        $resultado['test4'] = 'error dotenv: ' . $e->getMessage();
    }
} else {
    $resultado['test4'] = 'saltado - falta .env o vendor';
}

// Test 5: Probar conexión a base de datos
try {
    require_once __DIR__ . '/../models/conexion.php';
    $resultado['test5'] = 'conexion exitosa';
    $resultado['conexion_existe'] = isset($conn) ? 'SI' : 'NO';
} catch (Exception $e) {
    $resultado['test5'] = 'error conexion: ' . $e->getMessage();
}

// Devolver un solo objeto JSON
echo json_encode($resultado);
?>
