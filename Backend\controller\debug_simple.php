<?php
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");
header('Content-Type: application/json');

// Test 1: PHP básico
echo json_encode(['test1' => 'PHP funcionando']);

// Test 2: Verificar archivos
$archivos = [
    '.env' => file_exists(__DIR__ . '/../../.env'),
    'vendor' => file_exists(__DIR__ . '/../../vendor/autoload.php'),
    'conexion' => file_exists(__DIR__ . '/../models/conexion.php')
];

echo json_encode(['test2' => 'archivos', 'archivos' => $archivos]);

// Test 3: Intentar cargar vendor
if ($archivos['vendor']) {
    try {
        require_once __DIR__ . '/../../vendor/autoload.php';
        echo json_encode(['test3' => 'vendor cargado correctamente']);
    } catch (Exception $e) {
        echo json_encode(['test3' => 'error vendor', 'mensaje' => $e->getMessage()]);
    }
} else {
    echo json_encode(['test3' => 'vendor no existe']);
}

// Test 4: Intentar cargar .env
if ($archivos['.env'] && $archivos['vendor']) {
    try {
        $dotenv = Dotenv\Dotenv::createImmutable(__DIR__ . '/../..');
        $dotenv->load();
        echo json_encode(['test4' => 'dotenv cargado', 'env' => $_ENV['ENVIRONMENT'] ?? 'no definido']);
    } catch (Exception $e) {
        echo json_encode(['test4' => 'error dotenv', 'mensaje' => $e->getMessage()]);
    }
} else {
    echo json_encode(['test4' => 'saltado - falta .env o vendor']);
}
?>
