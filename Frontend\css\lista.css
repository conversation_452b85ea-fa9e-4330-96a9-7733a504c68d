:root {
  --primary: #ff6b35;
  --primary-dark: #ff5722;
}

* {
    margin: 0;
    box-sizing: border-box;
    font-family: Arial, Helvetica, sans-serif;
    text-align: center;
}

nav ul li {
float: left;
margin: 10px;
list-style: none;
}

/* Fondo negro para toda la página */
body {
  background-color: #f1f1f1;
  margin: 0;
  font-family: 'Space Grotesk', sans-serif;
  color: white;
  padding: 2rem;
}

footer {
    background-color: black;
    color: white;
    padding: 20px 30px;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
}

.footer-container {
    display: flex;
    justify-content: space-between;
    width: 100%;
    max-width: 1200px;
    margin: 0 ;
}

.container {
    max-width: 900px;
    padding: 0 15px;
    margin: auto;
}

.section {
    padding: 80px 0;
    min-height: 100vh;
    display: flex;
    justify-content: center;
}

.section {
    padding: 2rem;
    background-color: #ffffff;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
}

.navbar {
    background-color: white;
    padding: 0.5rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 9999;
}

/* Contenedor en fila, con scroll horizontal si se pasa */
.creatives-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  padding-top: 2rem;
  padding-bottom: 2rem;
  padding-left: 3rem;
  padding-right: 3rem;
}

/* Scroll bar pequeño y discreto */
.creatives-grid::-webkit-scrollbar {
  height: 6px;
}

.creatives-grid::-webkit-scrollbar-thumb {
  background-color: rgba(255, 165, 0, 0.3); /* naranja semi-transparente */
  border-radius: 3px;
}

/* Cards con fondo negro transparente y borde naranja */
.creative-card {
  flex: 0 0 auto; /* para que no se achiquen */
  width: 260px;
  background: rgba(17, 17, 17, 0.85); /* fondo negro oscuro (igual que navbar) con algo de transparencia */
  border: 2px solid orange; /* borde naranja */
  padding: 1.8rem 1.5rem;
  border-radius: 15px;
  transition: all 0.4s ease;
  cursor: pointer;
  color: white;
  box-shadow: 0 0 8px rgba(255, 165, 0, 0.3);
}

.creative-card:hover {
  background: rgba(255, 140, 0, 0.2);
  transform: translateY(-8px) scale(1.04);
  box-shadow: 0 0 12px orange;
  border-color: #ff7f00;
}

/* Imagen un poco más chica */
.creative-image {
  width: 110px;
  height: 110px;
  margin: 0 auto 1.8rem;
  position: relative;
}

.creative-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 55px;
  filter: none; /* sin filtros b/n */
  transition: all 0.4s ease;
}

/* Sin filtro al hover tampoco */
.creative-card:hover .creative-image img {
  filter: none;
}

/* El borde circular naranja alrededor de la imagen */
.creative-image::before {
  content: '';
  position: absolute;
  inset: -5px;
  border: 2px solid orange;
  border-radius: 100px;
  opacity: 0.35;
  transition: all 0.3s ease;
}

.creative-card:hover .creative-image::before {
  opacity: 1;
  transform: scale(1.1);
}

/* Texto */
.creative-name {
  font-size: 1.4rem;
  text-align: center;
  margin-bottom: 0.4rem;
  color: white;
  font-weight: 600;
}

.creative-role {
  color: orange;
  text-align: center;
  font-size: 1rem;
  margin-bottom: 1.3rem;
  font-weight: 500;
}

.creative-quote {
  color: rgba(255, 165, 0, 0.8);
  font-style: italic;
  text-align: center;
  line-height: 1.5;
  font-size: 0.9rem;
  margin-bottom: 1rem;
}

.creatives {
  background-color: #111;
}

/* Navbar ajustado con fondo #111 */
html, body {
  margin: 0;
  padding: 0;
  background-color: #111;
  font-family: 'Space Grotesk', sans-serif;
  color: white;
}

/* Navbar ajustado para pegarse arriba y ocupar todo el ancho */
.footer {
  background-color: #111;
  padding: 4rem 2rem 2.5rem;
  color: rgba(255, 255, 255, 0.8);
  font-family: 'Space Grotesk', sans-serif;
  border-top: 3px solid var(--primary); /* Línea separadora naranja */
  margin-top: 4rem;
}

.footer-grid {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  max-width: 100vw;
  width: 100vw;
  margin: 0;
  flex-wrap: wrap;
  gap: 1.5rem;
  padding-left: 0;
  padding-right: 0;
}

.footer-left {
  text-align: left;
  flex: 1 1 300px;
}

.footer-right {
  text-align: right;
  flex: 1 1 300px;
}

.footer-left h2 {
  color: var(--primary);
  font-size: 1.8rem;
  margin-bottom: 0.3rem;
}

.footer-left p {
  font-size: 0.9rem;
  color: rgba(255,255,255,0.6);
}

.footer-right p {
  margin: 0.2rem 0;
  font-size: 0.95rem;
}

.footer-right a {
  color: var(--primary);
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-right a:hover {
  color: var(--primary-dark);
  text-decoration: underline;
}

  @media (max-width: 600px) {
    .footer-grid {
      flex-direction: column;
      text-align: center;
      gap: 1rem;
    }

    .footer-right p {
      margin: 0.3rem 0;
    }

    
      .creatives-grid {
   grid-template-columns: minmax(200px, 1fr);
  }
    
  }
  
  
.navbar {
  position: sticky;
  top: 0;
  width: 100%;
  background-color: #111;
  border-bottom: 3px solid var(--primary); /* Línea separadora naranja */
  z-index: 1000;
  box-shadow: 0 2px 8px rgba(0,0,0,0.7);
  font-family: 'Space Grotesk', sans-serif;
}

.navbar-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 1rem 2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.navbar-logo {
  color: var(--primary);
  font-weight: 700;
  font-size: 1.8rem;
  cursor: pointer;
  user-select: none;
}

.navbar-menu {
  list-style: none;
  display: flex;
  gap: 2rem;
}

.navbar-menu li a {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.navbar-menu li a:hover,
.navbar-menu li a:focus {
  color: var(--primary);
}

.btn-perfil {
  display: block;
  width: 80%;
  padding: 0.8rem;
  background-color: #ff6b35; /* Color naranja */
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin: 1rem auto; /* Centra el botón horizontalmente */
  text-align: center;
}

.btn-perfil:hover {
  background-color: #ff5722; /* Naranja más oscuro al hover */
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(255, 107, 53, 0.3);
}