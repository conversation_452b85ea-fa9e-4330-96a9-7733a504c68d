<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <title>Crear Proyecto</title>
    <style>
        body { font-family: Arial, sans-serif; background: #f8fafc; padding: 2rem; }
        .form-container { background: #fff; max-width: 500px; margin: 0 auto; border-radius: 12px; box-shadow: 0 4px 16px rgba(0,0,0,0.08); padding: 2rem; }
        h2 { color: #ff6600; text-align: center; }
        label { font-weight: bold; display: block; margin-top: 1rem; }
        input, textarea { width: 100%; padding: 0.5rem; border-radius: 6px; border: 1px solid #ccc; margin-top: 0.5rem; }
        button { background: #ff6600; color: #fff; border: none; border-radius: 6px; padding: 0.75rem 1.5rem; font-weight: bold; margin-top: 1.5rem; cursor: pointer; width: 100%; }
        button:hover { background: #e65100; }
        #mensaje-proyecto { margin-top: 1rem; text-align: center; font-weight: bold; }
    </style>
</head>
<body>
    <div class="form-container">
        <h2>Agregar Proyecto</h2>
        <form id="form-proyecto" action="../../Backend/controller/crear_proyecto.php" method="POST" enctype="multipart/form-data">
            <label for="titulo_proyecto">Título del Proyecto:</label>
            <input type="text" id="titulo_proyecto" name="titulo_proyecto" required>

            <label for="descripcion">Descripción:</label>
            <textarea id="descripcion" name="descripcion" rows="3" required></textarea>

            <label for="id_pcreativa">ID del Creativo:</label>
            <input type="number" id="id_pcreativa" name="id_pcreativa" required>

            <label for="imagenes">Imágenes del Proyecto:</label>
            <input type="file" id="imagenes" name="imagenes[]" accept="image/*" multiple required>

            <button type="submit">Guardar Proyecto</button>
        </form>
        <div id="mensaje-proyecto"></div>
    </div>
    <script>
    document.getElementById('form-proyecto').addEventListener('submit', function(e) {
        e.preventDefault();
        const form = e.target;
        const data = new FormData(form);
        fetch(form.action, {
            method: 'POST',
            body: data
        })
        .then(res => res.text())
        .then(msg => {
            document.getElementById('mensaje-proyecto').innerText = msg;
            form.reset();
        })
        .catch(() => {
            document.getElementById('mensaje-proyecto').innerText = 'Error al guardar el proyecto';
        });
    });
    </script>
</body>
</html> 