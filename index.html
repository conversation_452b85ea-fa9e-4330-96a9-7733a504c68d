<!DOCTYPE html>
<html lang="es">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Match Creativo - Conectando Talento Creativo Local</title>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@400;500;600;700&display=swap" rel="stylesheet">
  <script src="https://unpkg.com/lucide@latest"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.5/gsap.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.5/ScrollTrigger.min.js"></script>
  <script src="Frontend/js/componentes.js" defer></script>
  
  <style>
     :root {
      --primary: #ff6b35;
      --primary-dark: #ff5722;
      --primary-light: #ff8a65;
      --secondary: #2d3436;
      --accent: #ffe0d6;
      --gradient: linear-gradient(135deg, var(--primary) 0%, #ff9f43 100%);
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      font-family: 'Space Grotesk', sans-serif;
    }

    body {
      background-color: #0a0a0a;
      color: #fff;
      overflow-x: hidden;
    }

    .container {
      max-width: 1400px;
      margin: 0 auto;
      padding: 0 2rem;
    }

    /* Hero Section */
    .hero {
      min-height: 100vh;
      position: relative;
      display: flex;
      align-items: center;
      background: #0a0a0a;
      overflow: hidden;
    }

    .hero-grid {
      position: absolute;
      inset: 0;
      background-size: 50px 50px;
      background-image: 
        linear-gradient(to right, rgba(255,255,255,0.05) 1px, transparent 1px),
        linear-gradient(to bottom, rgba(255,255,255,0.05) 1px, transparent 1px);
      transform: perspective(1000px) rotateX(60deg);
      transform-origin: center top;
      animation: grid 20s linear infinite;
    }

    @keyframes grid {
      to {
        background-position: 0 -50px;
      }
    }

    .hero-content {
      position: relative;
      z-index: 2;
      max-width: 800px;
      margin: 0 auto;
      text-align: center;
    }

    .hero-title {
      font-size: 7rem;
      font-weight: 700;
      line-height: 1;
      margin-bottom: 2rem;
      background: var(--gradient);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      opacity: 0;
      filter: blur(10px);
      transform: scale(0.8);
    }

    .hero-subtitle {
      font-size: 1.5rem;
      color: rgba(255,255,255,0.8);
      margin-bottom: 3rem;
      opacity: 0;
      transform: translateY(20px);
    }

    .stats-container {
      display: flex;
      justify-content: center;
      gap: 4rem;
      margin: 4rem 0;
    }

    .stat {
      opacity: 0;
      transform: translateY(20px);
    }

    .stat-number {
      font-size: 4rem;
      font-weight: 700;
      color: var(--primary);
      line-height: 1;
    }

    .stat-label {
      color: rgba(255,255,255,0.6);
      font-size: 1.1rem;
      margin-top: 0.5rem;
    }

    .cta-button {
      display: inline-flex;
      align-items: center;
      gap: 1rem;
      background: var(--gradient);
      color: white;
      padding: 1.2rem 3rem;
      border-radius: 100px;
      font-weight: 600;
      font-size: 1.2rem;
      text-decoration: none;
      transition: all 0.3s;
      opacity: 0;
      transform: translateY(20px);
      position: relative;
      overflow: hidden;
    }

    .cta-button::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
      transform: translateX(-100%);
      transition: transform 0.6s;
    }

    .cta-button:hover::before {
      transform: translateX(100%);
    }

    /* Vision Section */
    .vision {
      padding: 10rem 0;
      background: #111;
      position: relative;
    }

    .vision-title {
      font-size: 4rem;
      text-align: center;
      margin-bottom: 6rem;
      color: white;
      opacity: 0;
      transform: translateY(30px);
    }

    .vision-cards {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 2rem;
    }

    .vision-card {
      background: rgba(255,255,255,0.03);
      border: 1px solid rgba(255,255,255,0.1);
      padding: 3rem;
      border-radius: 20px;
      transition: all 0.5s;
      opacity: 0;
      transform: translateY(30px);
    }

    .vision-card:hover {
      background: rgba(255,255,255,0.05);
      transform: translateY(-10px);
    }

    .vision-card h3 {
      font-size: 2rem;
      color: var(--primary);
      margin-bottom: 1.5rem;
    }

    .vision-card p {
      color: rgba(255,255,255,0.7);
      line-height: 1.6;
    }
    
    

    /* About Section */
    .about {
      padding: 10rem 0;
      background: #0a0a0a;
      position: relative;
      overflow: hidden;
    }

    .about-content {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 6rem;
      align-items: center;
    }

    .about-text {
      opacity: 0;
      transform: translateX(-30px);
    }

    .about-text p {
      color: rgba(255,255,255,0.7);
      font-size: 1.2rem;
      line-height: 1.8;
      margin-bottom: 1.5rem;
    }

    .about-image {
      position: relative;
      opacity: 0;
      transform: translateX(30px);
    }

    .about-image img {
      width: 100%;
      border-radius: 20px;
      filter: brightness(0.8) contrast(1.2);
    }

    .about-image::before {
      content: '';
      position: absolute;
      inset: -20px;
      border: 2px solid var(--primary);
      border-radius: 30px;
      opacity: 0.3;
      transition: all 0.3s;
    }

    .about-image:hover::before {
      inset: -10px;
      opacity: 0.6;
    }
    
     /* News Section */
    .news {
      padding: 10rem 0;
      background: #0a0a0a;
      position: relative;
      overflow: hidden;
    }

    .news-content {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 6rem;
      align-items: center;
    }

    .news-text {
      opacity: 0;
      transform: translateX(-30px);
    }

    .news-text p {
      color: rgba(255,255,255,0.7);
      font-size: 1.2rem;
      line-height: 1.8;
      margin-bottom: 1.5rem;
    }

    .news-image {
      position: relative;
      opacity: 0;
      transform: translateX(30px);
    }

    .news-image img {
      width: 100%;
      border-radius: 20px;
      filter: brightness(0.8) contrast(1.2);
    }

    .news-image::before {
      content: '';
      position: absolute;
      inset: -20px;
      border: 2px solid var(--primary);
      border-radius: 30px;
      opacity: 0.3;
      transition: all 0.3s;
    }

    .news-image:hover::before {
      inset: -10px;
      opacity: 0.6;
    }

    .creatives-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 3rem;
}

/* Hago visible la card por defecto (quitando opacity:0 y transform) */
.creative-card {
  background: rgba(255,255,255,0.03);
  border: 1px solid rgba(255,255,255,0.1);
  padding: 2rem;
  border-radius: 20px;
  transition: all 0.5s;
  /* QUITO: opacity: 0; transform: translateY(30px); */
  opacity: 1 !important;
  transform: none !important;
}

.creative-card:hover {
  background: rgba(255,255,255,0.05);
  transform: translateY(-10px) scale(1.02);
}

.creative-image {
  width: 120px;
  height: 120px;
  margin: 0 auto 2rem;
  position: relative;
}

.creative-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 60px;
  filter: grayscale(100%) brightness(0.8);
  transition: all 0.5s;
}

.creative-card:hover .creative-image img {
  filter: grayscale(0%) brightness(1);
}

.creative-image::before {
  content: '';
  position: absolute;
  inset: -5px;
  border: 2px solid var(--primary);
  border-radius: 100px;
  opacity: 0.3;
  transition: all 0.3s;
}

.creative-card:hover .creative-image::before {
  opacity: 1;
  transform: scale(1.1);
}

.creative-name {
  font-size: 1.5rem;
  color: white;
  text-align: center;
  margin-bottom: 0.5rem;
}

.creative-role {
  color: var(--primary);
  text-align: center;
  font-size: 1rem;
  margin-bottom: 1.5rem;
}
    .creative-quote {
      color: rgba(255,255,255,0.7);
      text-align: center;
      font-style: italic;
      line-height: 1.6;
    }

    @media (max-width: 1024px) {
      .hero-title {
        font-size: 5rem;
      }
      
      .vision-cards {
        grid-template-columns: repeat(2, 1fr);
      }
    }

    @media (max-width: 768px) {
      .hero-title {
        font-size: 3.5rem;
      }

      .stats-container {
        flex-direction: column;
        gap: 2rem;
      }

      .vision-cards,
      .about-content,
      .news-content{
        grid-template-columns: 1fr;
      }

      .about-image,.news-content {
        order: -1;
      }
    }
    
  .footer {
  background-color: #111;
  padding: 4rem 2rem 2.5rem; /* Más padding arriba para separar */
  color: rgba(255, 255, 255, 0.8);
  font-family: 'Space Grotesk', sans-serif;
  border-top: 3px solid var(--primary); /* Línea separadora */
  margin-top: 4rem; /* Margen adicional para que no quede pegado */
}


  .footer-grid {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
    flex-wrap: wrap;
    gap: 1.5rem;
  }

  .footer-left h2 {
    color: var(--primary);
    font-size: 1.8rem;
    margin-bottom: 0.3rem;
  }

  .footer-left p {
    font-size: 0.9rem;
    color: rgba(255,255,255,0.6);
  }

  .footer-right p {
    margin: 0.2rem 0;
    font-size: 0.95rem;
  }

  .footer-right a {
    color: var(--primary);
    text-decoration: none;
    transition: color 0.3s ease;
  }

  .footer-right a:hover {
    color: var(--primary-dark);
    text-decoration: underline;
  }

  @media (max-width: 600px) {
    .footer-grid {
      flex-direction: column;
      text-align: center;
      gap: 1rem;
    }

    .footer-right p {
      margin: 0.3rem 0;
    }
  }
  .navbar {
  position: sticky;
  top: 0;
  width: 100%;
  background-color: #111;
  border-bottom: 3px solid var(--primary);
  z-index: 1000;
  box-shadow: 0 2px 8px rgba(0,0,0,0.7);
  font-family: 'Space Grotesk', sans-serif;
}

.navbar-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 1rem 2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.navbar-logo {
  color: var(--primary);
  font-weight: 700;
  font-size: 1.8rem;
  cursor: pointer;
  user-select: none;
}

.navbar-menu {
  list-style: none;
  display: flex;
  gap: 2rem;
}

.navbar-menu li a {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.navbar-menu li a:hover,
.navbar-menu li a:focus {
  color: var(--primary);
}
.btn-perfil {
  margin: 10px auto;
  padding: 8px 16px;
  background-color: #ff6b35; /* naranja */
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  display: block;
  width: 80%;
  text-align: center;
}

.btn-perfil:hover {
  background-color: #ff5722; /* naranja más oscuro */
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(255, 107, 53, 0.3);
} 

  </style>
</head>
<body>
<div id="header"></div>
<!--<nav class="navbar">
  <div class="container navbar-container">
    <div class="navbar-logo">Match Creativo</div>
    <ul class="navbar-menu">
      <li><a href="./Frontend/views/login.html">Inicio de sesion </a></li>
      <li><a href="#hero">Inicio</a></li>
      <li><a href="#vision">Misión</a></li>
      <li><a href="./Frontend/views/lista_creativos.html">Creativos</a></li>
      <li><a href="./Frontend/views/terminos.html">Términos y políticas</a></li>
    </ul>
  </div>-->
</nav>
  <section class="hero">
    <div class="hero-grid"></div>
    <div class="container">
      <div class="hero-content">
        <h1 class="hero-title">Match Creativo</h1>
        <p class="hero-subtitle">Conectando talento creativo local con oportunidades extraordinarias</p>
        
        <div class="stats-container">
          <div class="stat">
            <div class="stat-number">500+</div>
            <div class="stat-label">Oportunidades Generadas</div>
          </div>
          <div class="stat">
            <div class="stat-number">200+</div>
            <div class="stat-label">Creativos Conectados</div>
          </div>
          <div class="stat">
            <div class="stat-number">98%</div>
            <div class="stat-label">Satisfacción</div>
          </div>
        </div>

        <a href="https://chat.whatsapp.com/GeylFrhPnDIDCpV8qg2OVi" class="cta-button" target="_blank">
          Únete a la Comunidad
          <i data-lucide="arrow-right" width="24" height="24"></i>
        </a>
      </div>
    </div>
  </section>

  <section class="vision">
    <div class="container">
      <h2 class="vision-title">Nuestra Visión</h2>
      <div class="vision-cards">
        <div class="vision-card">
          <h3>Innovación</h3>
          <p>Transformamos la manera en que los creativos encuentran y aprovechan oportunidades profesionales.</p>
        </div>
        <div class="vision-card">
          <h3>Comunidad</h3>
          <p>Construimos una red vibrante de profesionales creativos que se apoyan mutuamente.</p>
        </div>
        <div class="vision-card">
          <h3>Impacto</h3>
          <p>Generamos un impacto positivo en la industria creativa y en la vida de nuestros usuarios.</p>
        </div>
      </div>
    </div>
  </section>

  <section class="about">
    <div class="container">
      <div class="about-content">
        <div class="about-text">
          <p>Match Creativo es una plataforma que conecta a profesionales del mundo creativo con quienes están buscando talento. Es un espacio donde puedes mostrar tu trabajo a través de un portafolio digital que realmente brilla.</p>
          <p>Aquí, las personas pueden encontrarte por tu especialidad, experiencia o ubicación. Y donde las oportunidades reales llegan a quienes tienen algo único que ofrecer.</p>
          <p>Para creativos: diseñadores, fotógrafos, ilustradores, artistas, escritores, entre otros. Para quienes buscan talento: empresas, emprendedores y organizaciones que necesitan creatividad de verdad.</p>
        </div>
        <div class="about-image">
          <img src="https://images.pexels.com/photos/3184291/pexels-photo-3184291.jpeg" 
               alt="Equipo creativo trabajando" 
               loading="lazy">
        </div>
      </div>
    </div>
  </section>

 
 
<section class="creatives">
  <div class="container">
    <div class="creatives-grid" id="lista-creativos">
      <div class="cargando">Cargando creativos...</div>
    </div>
  </div>
</section>

  <section class="news">
    
    <div class="container">
      <div class="news-content">
            <div class="news-image">
          <img src="./Assets/Imagenes/13e3418e-aea7-4357-938b-93d719cafe94-1-1536x915.jpeg" 
               alt="Equipo creativo trabajando" 
               loading="lazy">
        </div>
     
        <div class="news-text">
        <a href="https://elmagallanico.com/2024/10/proyecto-corfo-magallanes-creativo-y-su-primera-generacion-de-emprendimientos-sostenibles" target="_blank">           <h1>Prensa - El Magallanico</h1></a>
          <p><b>¡Magallanes ya es parte de la Red Nacional de Territorios Creativos!</b>

       Floan Luna, es periodista y coordinador regional del proyecto. Señaló que “hacer match el año pasado con Alex Paredes, líder de <a href="https://www.planeas.com/" target="_blank">Planeas.com</a> marcó un primer paso para avanzar decididamente con los actores públicos, privados, la academia y la ciudadanía, a robustecer el ecosistema creativo y cultural.</p>
          <p>Aquí, las personas pueden encontrarte por tu especialidad, experiencia o ubicación. Y donde las oportunidades reales llegan a quienes tienen algo único que ofrecer.</p>
          <p>Para creativos: diseñadores, fotógrafos, ilustradores, artistas, escritores, entre otros. Para quienes buscan talento: empresas, emprendedores y organizaciones que necesitan creatividad de verdad.</p>
        </div>
    
      </div>
      
    </div>
      
  </section>

  <center>
        <iframe  height="315" src="https://www.youtube.com/embed/tR-Ley-Qv0Q?si=J8LUmwQZSKxPZO3u" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>
      </center>

<div id="footer"></div>
      <!--<footer class="footer">
  <div class="container footer-grid">
    <div class="footer-left">
      <h2>Match Creativo</h2>
      <p>© 2025 una iniciativa Magallanica.</p>
     <p>© Alpha team Dev Ver 1.0.02 - última actualización 22-06-2025</p>
    </div>
    <div class="footer-right">
      <p><strong>Email:</strong> <EMAIL></p>
      <p><strong>Teléfono:</strong> +56 9 9044 0943 </p>
      <p><strong>Representante:</strong> Floan Luna </p>
      <p><strong>Red Social:</strong> <a href="https://instagram.com/matchcreativo" target="_blank" rel="noopener noreferrer">@matchcreativo</a></p>
    </div>
  </div>
</footer>-->
  <script>
    lucide.createIcons();

    // GSAP Animations
    gsap.registerPlugin(ScrollTrigger);

    // Hero Animations
    gsap.to('.hero-title', {
      opacity: 1,
      filter: 'blur(0px)',
      scale: 1,
      duration: 1.5,
      ease: 'power4.out'
    });

    gsap.to('.hero-subtitle', {
      opacity: 1,
      y: 0,
      duration: 1,
      delay: 0.5
    });

    gsap.to('.stat', {
      opacity: 1,
      y: 0,
      duration: 1,
      stagger: 0.2,
      delay: 0.8
    });

    gsap.to('.cta-button', {
      opacity: 1,
      y: 0,
      duration: 1,
      delay: 1.2
    });

    // Vision Cards Animation
    gsap.to('.vision-title', {
      scrollTrigger: {
        trigger: '.vision-title',
        start: 'top 80%'
      },
      opacity: 1,
      y: 0,
      duration: 1
    });

    gsap.to('.vision-card', {
      scrollTrigger: {
        trigger: '.vision-cards',
        start: 'top 80%'
      },
      opacity: 1,
      y: 0,
      duration: 1,
      stagger: 0.2
    });

    // About Section Animation
    gsap.to('.about-text', {
      scrollTrigger: {
        trigger: '.about-content',
        start: 'top 80%'
      },
      opacity: 1,
      x: 0,
      duration: 1
    });

    gsap.to('.about-image', {
      scrollTrigger: {
        trigger: '.about-content',
        start: 'top 80%'
      },
      opacity: 1,
      x: 0,
      duration: 1
    });
    
    

    // Creatives Cards Animation
    gsap.to('.creative-card', {
      scrollTrigger: {
        trigger: '.creatives-grid',
        start: 'top 80%'
      },
      opacity: 1,
      y: 0,
      duration: 1,
      stagger: 0.2
    });
    
    
        // news Section Animation
    gsap.to('.news-text', {
      scrollTrigger: {
        trigger: '.news-content',
        start: 'top 80%'
      },
      opacity: 1,
      x: 0,
      duration: 1
    });

    gsap.to('.news-image', {
      scrollTrigger: {
        trigger: '.news-content',
        start: 'top 80%'
      },
      opacity: 1,
      x: 0,
      duration: 1
    });
  </script>
  
  <script src="Frontend/js/contador_visitas.js"></script>
  <script src="Frontend/js/index.js"></script>
  <script src="Frontend/js/config.js"></script>



</body>
</html>
