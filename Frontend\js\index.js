document.addEventListener('DOMContentLoaded', function () {
    cargarCreativos();
});

async function cargarCreativos() {
    try {
        console.log('URL de la API:', `${window.API_URL_PHP}controller/get_creativos.php`);
        const response = await fetch(`${window.API_URL_PHP}controller/get_creativos.php`);

        console.log('Status de respuesta:', response.status);
        console.log('Headers de respuesta:', response.headers);

        // Verificar si la respuesta es exitosa
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        // Obtener el texto de la respuesta primero para debug
        const responseText = await response.text();
        console.log('Respuesta del servidor:', responseText);

        // Intentar parsear como JSON
        let data;
        try {
            data = JSON.parse(responseText);
        } catch (jsonError) {
            console.error('Error al parsear JSON:', jsonError);
            console.error('Respuesta recibida:', responseText);
            throw new Error('Respuesta del servidor no es JSON válido');
        }

        const listaContainer = document.getElementById('lista-creativos');

        const cargando = document.querySelector('.cargando');
        if (cargando) cargando.remove();

        if (data.exito && data.creativos.length > 0) {
            data.creativos.forEach((creativo, index) => {
                console.log(creativo); // para verificar
                let imgSrc = '../assets/default-profile.jpg';
                if (creativo.foto_perfil) imgSrc = creativo.foto_perfil;

                const descripcionCorta = creativo.descripcion && creativo.descripcion.length > 120
                    ? creativo.descripcion.substring(0, 120) + '...'
                    : (creativo.descripcion || '');

                const card = document.createElement('div');
                card.className = 'creative-card';

                card.innerHTML = `
                    <div class="creative-image">
                        <img src="${imgSrc}" alt="${creativo.nombre} ${creativo.apellido}" loading="lazy">
                    </div>
                    <h3 class="creative-name">${creativo.nombre} ${creativo.apellido}</h3>
                    <p class="creative-role">${creativo.titulo_profesional}</p>
                    <p class="creative-quote">"${descripcionCorta}"</p>
                    <button class="btn-perfil" onclick="verPerfil('${creativo.id_pcreativa}')">Ver Perfil</button>
                `;
                

                listaContainer.appendChild(card);
            });
        } else {
            listaContainer.innerHTML = `<div class="sin-resultados"><p>No se encontraron perfiles.</p></div>`;
        }
    } catch (error) {
        console.error('Error al cargar creativos:', error);
        document.getElementById('lista-creativos').innerHTML = `
            <div class="sin-resultados">
                <p>Error al cargar creativos.</p>
            </div>
        `;
    }
}

function verPerfil(id) {
    window.location.href = `./Frontend/views/perfil_creativo.html?id=${id}`;
}
