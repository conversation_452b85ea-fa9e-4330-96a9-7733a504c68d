document.addEventListener('DOMContentLoaded', function () {
    cargarCreativos();
});

async function cargarCreativos() {
    try {
        console.log('Testing basic PHP...');
        const response = await fetch(`${window.API_URL_PHP}controller/test_basic.php`);
        const responseText = await response.text();
        console.log('Basic test response:', responseText);

        const data = JSON.parse(responseText);

        const listaContainer = document.getElementById('lista-creativos');

        const cargando = document.querySelector('.cargando');
        if (cargando) cargando.remove();

        if (data.exito && data.creativos.length > 0) {
            data.creativos.forEach((creativo, index) => {
                console.log(creativo); // para verificar
                let imgSrc = '../assets/default-profile.jpg';
                if (creativo.foto_perfil) imgSrc = creativo.foto_perfil;

                const descripcionCorta = creativo.descripcion && creativo.descripcion.length > 120
                    ? creativo.descripcion.substring(0, 120) + '...'
                    : (creativo.descripcion || '');

                const card = document.createElement('div');
                card.className = 'creative-card';

                card.innerHTML = `
                    <div class="creative-image">
                        <img src="${imgSrc}" alt="${creativo.nombre} ${creativo.apellido}" loading="lazy">
                    </div>
                    <h3 class="creative-name">${creativo.nombre} ${creativo.apellido}</h3>
                    <p class="creative-role">${creativo.titulo_profesional}</p>
                    <p class="creative-quote">"${descripcionCorta}"</p>
                    <button class="btn-perfil" onclick="verPerfil('${creativo.id_pcreativa}')">Ver Perfil</button>
                `;
                

                listaContainer.appendChild(card);
            });
        } else {
            listaContainer.innerHTML = `<div class="sin-resultados"><p>No se encontraron perfiles.</p></div>`;
        }
    } catch (error) {
        console.error('Error al cargar creativos:', error);
        document.getElementById('lista-creativos').innerHTML = `
            <div class="sin-resultados">
                <p>Error al cargar creativos.</p>
            </div>
        `;
    }
}

function verPerfil(id) {
    window.location.href = `./Frontend/views/perfil_creativo.html?id=${id}`;
}
