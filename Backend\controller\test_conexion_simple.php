<?php
header("Access-Control-Allow-Origin: *");
header("Access-Control-Allow-Methods: GET, OPTIONS");
header("Access-Control-Allow-Headers: Content-Type");
header('Content-Type: application/json');

$resultado = [];

// Test 1: Cargar vendor
try {
    require_once __DIR__ . '/../../vendor/autoload.php';
    $resultado['vendor'] = 'cargado correctamente';
} catch (Exception $e) {
    $resultado['vendor'] = 'error: ' . $e->getMessage();
    echo json_encode($resultado);
    exit;
}

// Test 2: Cargar .env
try {
    $dotenv = Dotenv\Dotenv::createImmutable(__DIR__ . '/../..');
    $dotenv->load();
    $resultado['dotenv'] = 'cargado correctamente';
    $resultado['environment'] = $_ENV['ENVIRONMENT'] ?? 'no definido';
} catch (Exception $e) {
    $resultado['dotenv'] = 'error: ' . $e->getMessage();
    echo json_encode($resultado);
    exit;
}

// Test 3: Verificar variables de entorno
$environment = $_ENV['ENVIRONMENT'] ?? 'development';
if($environment === 'production') {  
    $host = $_ENV['PROD_DB_HOST'] ?? 'NO_DEFINIDO';
    $port = $_ENV['PROD_DB_PORT'] ?? 'NO_DEFINIDO';
    $user = $_ENV['PROD_DB_USER'] ?? 'NO_DEFINIDO';
    $password = $_ENV['PROD_DB_PASSWORD'] ?? 'NO_DEFINIDO';
    $nameDb = $_ENV['PROD_DB_NAME'] ?? 'NO_DEFINIDO';
} else {
    $host = $_ENV['DEV_DB_HOST'] ?? 'NO_DEFINIDO';
    $port = $_ENV['DEV_DB_PORT'] ?? 'NO_DEFINIDO';
    $user = $_ENV['DEV_DB_USER'] ?? 'NO_DEFINIDO';
    $password = $_ENV['DEV_DB_PASSWORD'] ?? 'NO_DEFINIDO';
    $nameDb = $_ENV['DEV_DB_NAME'] ?? 'NO_DEFINIDO';
}

$resultado['variables_env'] = [
    'host' => $host,
    'port' => $port,
    'user' => $user,
    'password' => substr($password, 0, 3) . '***', // Solo mostrar primeros 3 caracteres
    'database' => $nameDb
];

// Test 4: Intentar conexión
$dsn = "mysql:host=$host;port=$port;dbname=$nameDb";
try {
    $conn = new PDO($dsn, $user, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
    ]);
    $resultado['conexion'] = 'exitosa';
    
    // Test 5: Probar una consulta simple
    $stmt = $conn->prepare("SELECT COUNT(*) as total FROM perfil_creativo");
    $stmt->execute();
    $count = $stmt->fetch(PDO::FETCH_ASSOC);
    $resultado['perfil_creativo_count'] = $count['total'];
    
} catch (PDOException $e) {
    $resultado['conexion'] = 'error: ' . $e->getMessage();
}

echo json_encode($resultado, JSON_PRETTY_PRINT);
?>
